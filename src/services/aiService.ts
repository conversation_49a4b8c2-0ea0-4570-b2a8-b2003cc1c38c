import OpenAI from "openai";
import type { Msg, MessageContent, ImageData } from "@/core/types/chat.ts"; // Added ImageData
import { messageService } from "./messageService";
import { imageDb } from "@/persistence/image.db"; // Import imageDb
import { contentToString } from "@/core/utils/messageUtils.ts"; // Import contentToString

export interface SendPromptOptions {
    messages: OpenAI.ChatCompletionMessageParam[];
    model: string;
    modelProvider?: string; // 模型所属的供应商
    onNewChunk?: (chunk: string) => void;
    onError?: (error: any) => void;
    onStart?: () => void;
    onComplete?: (fullResponse: string) => void;
}

export interface SendToLLMOptions {
    resend?: boolean;
    assistantMessageId?: string; // AI需要更新的消息ID (由Store创建和传入)
    maxMsgCount?: number;
    onNewChunk?: (chunk: string) => void; // 添加此行
    systemPrompt?: string; // 系统提示词
}

/**
 * AI服务 - 处理与AI模型的交互
 */
export class AIService {
    /**
     * 发送提示到OpenAI并以流的方式处理响应
     */
    async sendPrompt(
        openai: OpenAI,
        options: SendPromptOptions
    ): Promise<string | null> {
        const { messages, model, onNewChunk, onError, onStart, onComplete } =
            options;

        if (onStart) {
            onStart();
        }

        try {
            console.log("API Request Messages:", messages); // Log messages sent to API
            const stream = await openai.chat.completions.create(
                {
                    model,
                    messages,
                    stream: true,
                },
                {
                    maxRetries: 0,
                }
            );

            let fullResponse = "";
            for await (const chunk of stream) {
                const content = chunk.choices[0]?.delta?.content || "";
                fullResponse += content;

                if (onNewChunk && content) {
                    console.log("[AIService] Calling onNewChunk with content:", content);
                    onNewChunk(content);
                } else if (!onNewChunk) {
                    console.warn("[AIService] onNewChunk callback is not provided");
                }
            }

            if (onComplete) {
                onComplete(fullResponse);
            }
            console.log("API Full Response:", fullResponse);
            return fullResponse;
        } catch (error) {
            if (onError) {
                onError(error);
            }
            console.error("API请求错误:", error);
            // 重新抛出错误，以便上层可以捕获并处理
            throw error;
        }
    }

    /**
     * Helper function to convert Blob to Base64 Data URL
     */
    private async blobToDataURL(blob: Blob): Promise<string> {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onloadend = () => {
                if (typeof reader.result === "string") {
                    resolve(reader.result);
                } else {
                    reject(new Error("Failed to read blob as Data URL."));
                }
            };
            reader.onerror = reject;
            reader.readAsDataURL(blob);
        });
    }

    /**
     * 将消息内容转换为OpenAI API所需格式
     */
    async prepareMessagesForAPI(
        messages: Msg[]
    ): Promise<OpenAI.ChatCompletionMessageParam[]> {
        const preparedMessages: OpenAI.ChatCompletionMessageParam[] = [];

        for (const msg of messages) {
            const content = msg.content;
            let apiMessage: OpenAI.ChatCompletionMessageParam;

            if (msg.role === "user") {
                if (typeof content === "string") {
                    apiMessage = { role: "user", content };
                } else if (Array.isArray(content)) {
                    // 混合内容 (文本+图片)
                    const apiContentParts: OpenAI.ChatCompletionContentPart[] =
                        await Promise.all(
                            content.map(
                                async (
                                    item
                                ): Promise<OpenAI.ChatCompletionContentPart> => {
                                    if (typeof item === "string") {
                                        return { type: "text", text: item };
                                    } else if (item.type === "image") {
                                        const imageRecord =
                                            await imageDb.getImage(
                                                item.imageId
                                            );
                                        if (imageRecord && imageRecord.blob) {
                                            const dataUrl =
                                                await this.blobToDataURL(
                                                    imageRecord.blob
                                                );
                                            return {
                                                type: "image_url", // No 'as const' needed due to explicit return type
                                                image_url: { url: dataUrl },
                                            };
                                        }
                                        console.warn(
                                            `Image with ID ${item.imageId} not found in imageDb.`
                                        );
                                        return {
                                            type: "text",
                                            text: `[Image ${
                                                item.alt || item.imageId
                                            } not found]`,
                                        };
                                    }
                                    return { type: "text", text: "" }; // Should not happen
                                }
                            )
                        );
                    apiMessage = { role: "user", content: apiContentParts };
                } else if (content && content.type === "image") {
                    // 单张图片
                    const imageRecord = await imageDb.getImage(content.imageId);
                    if (imageRecord && imageRecord.blob) {
                        const dataUrl = await this.blobToDataURL(
                            imageRecord.blob
                        );
                        apiMessage = {
                            role: "user",
                            content: [
                                {
                                    type: "image_url",
                                    image_url: { url: dataUrl },
                                },
                            ],
                        };
                    } else {
                        console.warn(
                            `Image with ID ${content.imageId} not found in imageDb.`
                        );
                        apiMessage = {
                            role: "user",
                            content: `[Image ${
                                content.alt || content.imageId
                            } not found]`,
                        };
                    }
                } else {
                    apiMessage = { role: "user", content: "" }; // Fallback for unrecognized content
                }
            } else if (msg.role === "assistant") {
                apiMessage = {
                    role: "assistant",
                    content:
                        typeof content === "string"
                            ? content
                            : contentToString(content),
                };
            } else {
                // Handle other roles or throw error if unexpected role
                console.error(`Unhandled message role: ${msg.role}`);
                // Skip this message or create a default/error message
                continue; // Simple skip for now
            }
            preparedMessages.push(apiMessage);
        }
        return preparedMessages;
    }

    /**
     * 发送消息到LLM并处理响应 (根据重构计划，方法名调整为 sendMessageToAI)
     * 重构版本：此方法现在专注于调用LLM并更新由Store传入的assistantMessageId对应的消息。
     * 用户消息和AI占位消息的创建由Store负责。
     */
    async sendMessageToAI(
        chatId: string,
        openai: OpenAI,
        model: string,
        msgContent: MessageContent | null, // 用户原始输入，用于构建上下文。如果是重新生成，则为null。
        options: SendToLLMOptions = {}
    ): Promise<string> {
        const {
            resend,
            assistantMessageId, // This is the ID of the assistant's message (placeholder) to update.
            maxMsgCount = 10,
            systemPrompt,
        } = options;

        const targetAssistantMsgId = assistantMessageId;

        if (!targetAssistantMsgId) {
            console.error(
                "AIService: Target assistant message ID (assistantMessageId or resend msgId) is not provided."
            );
            return "";
        }

        try {
            const allMessages = await messageService.getChatMessages(chatId);
            let historyForContext: Msg[];

            if (resend) {
                const retryMsgIndex = allMessages.findIndex(
                    (msg) => msg.id === targetAssistantMsgId
                );
                if (retryMsgIndex !== -1) {
                    historyForContext = allMessages.slice(0, retryMsgIndex);
                } else {
                    historyForContext = allMessages.filter(
                        (msg) => msg.id !== targetAssistantMsgId
                    );
                }
            } else {
                historyForContext = allMessages.filter(
                    (msg) => msg.id !== targetAssistantMsgId
                );
            }

            if (maxMsgCount > 0 && historyForContext.length > maxMsgCount) {
                historyForContext = historyForContext.slice(-maxMsgCount);
            }

            const apiMessages = await this.prepareMessagesForAPI(
                historyForContext
            );

            // 如果有系统提示词，添加到消息列表开头
            if (systemPrompt && systemPrompt.trim()) {
                apiMessages.unshift({
                    role: "system",
                    content: systemPrompt.trim()
                });
            }

            const fullResponse = await this.sendPrompt(openai, {
                messages: apiMessages,
                model: model,
                onNewChunk: (chunk) => {
                    console.log("[AIService] sendMessageToAI onNewChunk called with chunk:", chunk);
                    // 在这里，我们需要一种方式将chunk传递回调用者
                    // 最好的方式是通过一个新的回调选项
                    if (options.onNewChunk) {
                        console.log("[AIService] Forwarding chunk to options.onNewChunk");
                        options.onNewChunk(chunk);
                    } else {
                        console.warn("[AIService] options.onNewChunk is not provided");
                    }
                },
            });

            return fullResponse || "";
        } catch (error) {
            console.error("AI服务错误 (sendMessageToAI catch):", error);
            throw error;
        }
    }

    /**
     * 检查字符串是否已经是 Data URL 格式
     */
    private isDataUrl(str: string): boolean {
        return /^data:([a-z]+\/[a-z0-9-+.]+)?;base64,/i.test(str);
    }

    /**
     * 确保字符串是 Data URL 格式
     */
    private ensureDataUrl(data: string, mimeType: string): string {
        if (this.isDataUrl(data)) {
            return data; // 已经是 Data URL 格式，直接返回
        }
        return `data:${mimeType || "image/jpeg"};base64,${data}`;
    }
}

export const aiService = new AIService();
