import type { OpenAI } from "openai";

/**
 * 摘要服务 - 负责调用 AI 模型生成文本摘要。
 */
export class SummaryService {
    /**
     * 调用 AI 模型生成文本摘要。
     * @param openai - OpenAI 实例
     * @param model - 使用的模型
     * @param originText - 需要生成摘要的原始文本
     * @returns 生成的摘要文本
     * @throws 如果 AI 调用失败，则会抛出错误
     */
    async generateSummary(
        openai: OpenAI,
        model: string,
        originText: string
    ): Promise<string> {
        try {
            const completion = await openai.chat.completions.create({
                model: model,
                messages: [
                    {
                        role: "user",
                        content: `任务描述：
请对以下用户提问或回答进行总结，确保总结内容简洁明了。
待总结文本：
{{${originText}}}
要求：
总结长度： 总结内容应简洁明了，不超过20个字。
语言风格： 保持客观、中立，避免主观判断。
示例：
原文本： “我想知道如何提高英语口语能力，尤其是发音和流利度。”
总结： 提高英语口语，发音与流利度。
原文本： “今天天气真好，阳光明媚，适合出去散步。”
总结： 天气好，适合散步。
原文本： “我昨天去了超市，买了牛奶、面包和水果。”
总结： 超市购物，买牛奶面包水果。
原文本： “这个问题比较复杂，需要进一步分析。”
总结： 问题复杂，需进一步分析。
原文本： “你好！”
总结： 你好！
`,
                    },
                ],
            });

            const result = completion.choices[0].message.content || originText;
            console.log("已生成缩略", result);
            return result;
        } catch (error) {
            console.error("总结生成失败:", error);
            // 重新抛出错误，以便上层调用者（如Composable）可以捕获并处理
            throw new Error("总结生成失败，请稍后再试");
        }
    }
}

// 导出单例实例
export const summaryService = new SummaryService();
