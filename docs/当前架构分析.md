# 当前架构详细分析

**分析时间**: 2025-06-28  
**分析目标**: 梳理现有代码结构，制定具体的迁移计划

## 1. 当前代码结构分析

### 1.1 新架构实现状况 ✅

#### DDD 层级结构
```
src/
├── domains/                    # 领域层
│   ├── ai/                    # AI 相关领域
│   ├── chat/                  # 聊天领域  
│   └── message/               # 消息领域
├── usecases/                  # 用例层
│   ├── base/                  # 用例基类
│   │   └── UseCase.ts         # IUseCase 接口定义
│   ├── message/               # 消息相关用例
│   │   ├── SendMessageUseCase.ts
│   │   ├── ProcessThinkingUseCase.ts
│   │   └── UpdateMessageUseCase.ts
│   ├── chat/                  # 聊天相关用例
│   │   ├── CreateChatUseCase.ts
│   │   ├── LoadChatUseCase.ts
│   │   ├── RemoveChatUseCase.ts
│   │   ├── UpdateChatUseCase.ts
│   │   └── LoadAllChatsUseCase.ts
│   ├── ai/                    # AI 相关用例
│   └── container/             # 依赖注入
│       └── DIContainer.ts     # 依赖注入容器
├── infrastructure/            # 基础设施层
│   ├── MessageRepository.ts   # 消息仓储接口
│   ├── ChatRepository.ts      # 聊天仓储接口
│   ├── AIProvider.ts          # AI 提供者接口
│   └── impl/                  # 具体实现
│       ├── MessageRepositoryImpl.ts
│       ├── ChatRepositoryImpl.ts
│       └── AIProviderImpl.ts
├── adapters/                  # 适配器层
│   └── ChatStoreAdapter.ts    # 主要业务适配器
└── core/                      # 核心层
    ├── types/                 # 类型定义
    ├── config/                # 配置
    └── utils/                 # 工具函数
```

#### 实现状态评估
- ✅ **用例层**: 核心用例已完整实现
- ✅ **基础设施层**: 仓储模式已实现，适配现有服务
- ✅ **适配器层**: ChatStoreAdapter 提供统一接口
- ✅ **依赖注入**: DIContainer 管理所有依赖关系
- ⚠️ **领域层**: 目录存在但实现较少
- ⚠️ **UI 集成**: 部分组件仍直接使用旧架构

### 1.2 遗留架构分析 🔧

#### Services 层现状
```
src/services/
├── aiService.ts           # AI 服务 - 职责过重 ⚠️
├── chatService.ts         # 聊天服务 - 数据访问为主 ✅
├── messageService.ts      # 消息服务 - 数据访问为主 ✅
├── imageService.ts        # 图片服务 - 独立功能 ✅
├── modelService.ts        # 模型服务 - 配置管理 ✅
├── summaryService.ts      # 摘要服务 - 业务逻辑 ⚠️
└── validatorService.ts    # 验证服务 - 工具函数 ✅
```

**问题分析**:
- `aiService.ts`: 承担了 API 调用、流处理、消息转换等多重职责
- `summaryService.ts`: 包含业务逻辑，应迁移到用例层
- 其他服务主要是数据访问，可保留作为 Repository 实现

#### Store 层现状
```
src/store/
├── useChatStore.ts        # 聊天状态管理 - 核心 Store ⚠️
├── useEditAreaStore.ts    # 编辑区状态 - UI 状态 ✅
├── useEnvStore.ts         # 环境配置 - 配置管理 ✅
├── useGuideStore.ts       # 引导状态 - UI 状态 ✅
└── useModelStore.ts       # 模型配置 - 配置管理 ✅
```

**问题分析**:
- `useChatStore.ts`: 包含大量业务逻辑，需要重构
- 其他 Store 主要是 UI 状态和配置，可保留

#### Composables 层现状
```
src/composables/
└── useSummary.ts          # 摘要功能 - 业务逻辑 ⚠️
```

**问题分析**:
- `useSummary.ts`: 包含业务逻辑，应迁移到用例层

#### Persistence 层现状
```
src/persistence/
├── chat.db.ts             # 聊天数据库操作 ✅
├── message.db.ts          # 消息数据库操作 ✅
└── image.db.ts            # 图片数据库操作 ✅
```

**状态评估**: 
- 数据访问层实现良好，已被新架构的 Repository 层适配

## 2. 依赖关系分析

### 2.1 当前依赖图

```mermaid
graph TD
    A[Vue Components] --> B[useChatStore]
    A --> C[Services]
    B --> C
    C --> D[Persistence]
    
    E[ChatStoreAdapter] --> F[UseCases]
    F --> G[Repositories]
    G --> C
    
    style A fill:#e1f5fe
    style B fill:#fff3e0
    style C fill:#ffebee
    style E fill:#e8f5e8
    style F fill:#e8f5e8
    style G fill:#e8f5e8
```

### 2.2 问题识别

1. **双重依赖路径**: 组件既可以通过 Store 也可以通过 Adapter 访问业务逻辑
2. **循环依赖风险**: Store 和 Service 之间可能存在循环引用
3. **测试困难**: 组件直接依赖具体实现，难以进行单元测试

## 3. 迁移优先级分析

### 3.1 高优先级 (立即处理)

#### 🔥 UI 层迁移
**目标**: 所有 Vue 组件使用 ChatStoreAdapter 而非直接调用 Services

**涉及文件**:
- `src/components/EditArea/` - 消息输入组件
- `src/components/MsgList/` - 消息列表组件  
- `src/components/ChatList/` - 聊天列表组件
- `src/views/Chat/` - 聊天页面
- `src/views/Setting/` - 设置页面

**迁移策略**:
```typescript
// 旧方式
import { aiService } from '@/services/aiService'
await aiService.sendMessage(...)

// 新方式  
import { chatStoreAdapter } from '@/adapters/ChatStoreAdapter'
await chatStoreAdapter.sendMessage(...)
```

#### 🔥 Store 重构
**目标**: useChatStore 仅负责状态管理，业务逻辑移到用例层

**重构计划**:
1. 提取业务方法到 ChatStoreAdapter
2. Store 只保留状态和响应式更新
3. 通过 Adapter 调用用例层

### 3.2 中优先级 (后续处理)

#### ⚠️ Services 层简化
**目标**: 保留数据访问功能，移除业务逻辑

**处理方案**:
- `aiService.ts`: 拆分为多个用例，保留 API 调用部分
- `summaryService.ts`: 业务逻辑移到用例层
- 其他服务: 保持现状，作为 Repository 实现的底层

#### ⚠️ Composables 迁移
**目标**: 将业务逻辑型 Composables 迁移到用例层

**处理方案**:
- `useSummary.ts`: 创建 SummaryUseCase，保留响应式包装

### 3.3 低优先级 (长期优化)

#### 📋 领域层完善
**目标**: 丰富领域模型，提取业务规则

**计划**:
- 完善 Message、Chat 等实体
- 提取业务规则到领域服务
- 实现领域事件机制

#### 📋 架构优化
**目标**: 性能优化和代码质量提升

**计划**:
- 实现用例级缓存
- 优化依赖注入性能
- 完善错误处理机制

## 4. 具体迁移计划

### 4.1 第一周: UI 层迁移

**Day 1-2**: EditArea 组件迁移
- 重构消息发送逻辑
- 使用 ChatStoreAdapter.sendMessage
- 测试消息发送功能

**Day 3-4**: MsgList 和 MsgItem 组件迁移  
- 重构消息显示逻辑
- 使用 ChatStoreAdapter 的消息更新接口
- 测试消息状态更新

**Day 5**: ChatList 组件迁移
- 重构聊天列表管理
- 使用 ChatStoreAdapter 的聊天管理接口
- 测试聊天创建、删除、重命名

### 4.2 第二周: Store 重构

**Day 1-3**: useChatStore 重构
- 提取业务方法到 ChatStoreAdapter
- 简化 Store 为纯状态管理
- 保持 API 兼容性

**Day 4-5**: 其他 Store 优化
- 检查其他 Store 的业务逻辑
- 必要时进行类似重构
- 完善类型定义

### 4.3 第三周: Services 层清理

**Day 1-3**: aiService 重构
- 将流处理逻辑移到用例层
- 保留纯 API 调用功能
- 更新相关依赖

**Day 4-5**: 其他 Services 优化
- summaryService 迁移到用例层
- 清理未使用的代码
- 完善错误处理

## 5. 风险评估和缓解

### 5.1 主要风险

1. **功能回归**: 迁移过程中可能引入 Bug
2. **性能影响**: 新架构可能影响性能
3. **开发效率**: 短期内开发效率可能下降

### 5.2 缓解措施

1. **渐进迁移**: 每次只迁移一个模块
2. **充分测试**: 每个阶段都进行完整测试
3. **性能监控**: 实时监控关键指标
4. **回滚准备**: 保持代码可回滚状态

## 6. 成功指标

### 6.1 技术指标

- [ ] 所有 UI 组件使用适配器层
- [ ] Store 层职责单一化
- [ ] Services 层简化完成
- [ ] 单元测试覆盖率 > 80%

### 6.2 质量指标

- [ ] 代码复杂度降低 30%
- [ ] 构建时间保持稳定
- [ ] 运行时性能无明显下降
- [ ] 新功能开发效率提升

---

**下一步行动**: 开始 UI 层迁移，从 EditArea 组件开始
